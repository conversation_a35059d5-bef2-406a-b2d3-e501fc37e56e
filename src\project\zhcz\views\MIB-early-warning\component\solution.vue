<!-- 解决方案 -->
<template>
  <div class="solution">
    <HLCard
      className="custom-card"
      :styleData="{ borderRadius: '16px' }"
      themeColor="Light"
      :line="true"
    >
      <template #headerLeftAfter>
        <div class="text__header">解决方案</div>
        <!-- <Icon icon="mdi:card" /> -->
      </template>

      <template #defaultBody>
        <ul class="solution__list">
          <li v-for="(item, index) in dataList" :key="index">
            <span class="solution__index">{{ index + 1 }}.</span>{{ item }}
          </li>
        </ul>
      </template>
    </HLCard>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  // import { HLCard } from 'lutwelve-components';
  // import 'lutwelve-components/dist/style.css';
  const dataList = ref([
    '通过加大水库下泄流量，可加快水体流动，增强污染物扩散与稀释能力',
    '启用备用水源或跨区域调水，优先调配低等级污染水体',
  ]);
</script>

<style lang="less" scoped>
  .solution {
    width: 100%;

    // padding: 24px 32px;
    // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    // backdrop-filter: blur(10px);

    .custom-card {
      border-radius: 16px;
      border: 2px solid #ffffff;
      background: rgba(247, 250, 254, 1);

      :deep(.base-header) {
        height: 56px;
        padding: 16px 24px;
      }

      :deep(.base-header_line) {
        border-bottom: 1px solid #bcc5ce;
      }

      :deep(.card-body) {
        padding: 16px 24px;
      }
    }

    .text__header {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 24px;
      line-height: 24px;
      color: #000000;
      position: relative;
      cursor: pointer;
    }

    .solution__list {
      list-style: none;
      margin: 0;
      width: 100%;
      // height: 72px;
      overflow-y: auto;

      li {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 22px;
        color: #000000;
        line-height: 36px;

        .solution__index {
          // color: #2196f3;
          font-weight: bold;
          margin-right: 8px;
          min-width: 20px;
        }
      }
    }
  }
</style>
