export default [
  {
    path: '/sword-shrimp',
    name: 'swordShrimp',
    component: () => import('/@zhcz/views/sword-shrimp/index.vue'),
    // meta: {
    //   title: '剑水蚤统计',
    //   ignoreAuth: true,
    // },
  },
  {
    path: '/mib',
    name: 'mib',
    component: () => import('/@zhcz/views/MIB-early-warning/index.vue'),
    children: [
      {
        path: 'data',
        name: 'dataMonitoring',
        component: () =>
          import('/@zhcz/views/MIB-early-warning/childComponents/dataMonitoring/index.vue'),
      },
      {
        path: 'algae',
        name: 'algae',
        component: () => import('/@zhcz/views/MIB-early-warning/childComponents/algae/index.vue'),
      },
    ],
    // meta: {
    //   title: 'MIB预警',
    //   ignoreAuth: true,
    // },
  },
];
