<!-- 数据监测 -->
<template>
  <div class="algae-monitoring">
    <weatherBar :updata="updataTime" @openDialog="openDialog" />
    <div @click="goBack('mib')">数据监测详情</div>
    <div class="content-sections">
      <solution :updata="updataTime" />
      <futurePredictions :updata="updataTime" />
      <onlineMonitoringOfAlgae :updata="updataTime" />
      <historicalMonitoringData :updata="updataTime" @openDialog="openDialog" />
    </div>
    <div class="footer">
      <img src="../../assets/images/water-technology.png" alt="" srcset="" />
    </div>
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :title="currentDetailTitle"
      :data-type="currentDetailType"
      @close="closeDetailDialog"
    />
  </div>
</template>

<script lang="ts" setup name="MIBEarlyWarning">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useIntervalFn } from '@vueuse/core';
  import weatherBar from '../../component/weather-bar.vue';
  import solution from '../../component/solution.vue';
  import futurePredictions from '../../component/future-predictions.vue';
  import onlineMonitoringOfAlgae from '../../component/online-monitoring-of-algae.vue';
  import historicalMonitoringData from '../../component/historical-monitoring-data.vue';
  import DetailDialog from '../../component/detail-dialog.vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  // 详情弹框相关状态
  const detailDialogVisible = ref(false);
  const currentDetailType = ref('');
  const currentDetailTitle = ref('');
  const closeDetailDialog = () => {
    detailDialogVisible.value = false;
    currentDetailType.value = '';
    currentDetailTitle.value = '';
  };
  const openDialog = (item: any) => {
    console.log(`打开 ${item} 详情`, item);
    currentDetailType.value = item.indexCode;
    currentDetailTitle.value = item.indexName || '';
    detailDialogVisible.value = true;
    // console.log(`打开 ${currentDetailTitle.value} 详情`);
  };
  const updataTime = ref<number>(1);
  const getAllData = () => {
    updataTime.value += 1;
  };
  const goBack = (path: string) => {
    // 跳回上级路由 /mib
    router.push({ path });
  };
  const { resume, pause } = useIntervalFn(getAllData, 6 * 1000);
  onUnmounted(() => {
    pause();
  });
  onMounted(() => {
    resume();
  });
</script>

<style lang="less" scoped>
  .algae-monitoring {
    // width: 100vw;
    max-width: 1080px;
    // min-width: 1080px;
    max-height: 1920px;
    // min-height: 800px;
    margin: 0 auto;
    background: url('../../assets/images/center-bg.png') no-repeat center center;
    // background: linear-gradient(180deg, #ffffff 0%, #d8e8f9 100%);
    background-size: 100% 100%;
    border-radius: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: 0;
    overflow-y: auto;
    position: relative;

    .early_left {
      position: absolute;
      top: 13.95%;
      left: 36px;
    }

    /* 响应式适配 */
    // @media (max-width: 1200px) {
    //   min-width: 100vw;
    //   padding: 0;

    //   .title-section .main-title {
    //     font-size: 24px;
    //   }

    //   .content-sections {
    //     padding: 0 16px 16px;
    //     gap: 16px;
    //   }

    //   .footer {
    //     padding: 16px 0;

    //     .footer-logo {
    //       .logo-text {
    //         font-size: 24px;
    //       }

    //       .logo-desc {
    //         font-size: 14px;
    //       }
    //     }
    //   }
    // }

    .content-sections {
      width: 100%;
      max-width: 1600px;
      padding: 0 24px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .footer {
      width: 100%;
      height: 64px;
      margin: auto;
      text-align: center;

      & > img {
        height: 100%;
      }
    }
  }

  // .weather-bar {
  //   width: 100%;
  //   max-width: 1600px;
  //   min-height: 60px;
  //   margin-bottom: 12px;
  // }

  // .center-image {
  //   width: 100%;
  //   max-width: 900px;
  //   min-height: 400px;
  //   margin: 0 auto 16px auto;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  // }

  // .solution,
  // .future-predictions,
  // .algae-monitoring,
  // .historical-monitoring-data {
  //   width: 100%;
  //   max-width: 1600px;
  //   margin: 0 auto 16px auto;
  //   border-radius: 16px;
  //   box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  //   background: #fff;
  //   padding: 24px 32px;
  //   box-sizing: border-box;
  // }

  // @media (max-width: 1200px) {
  //   .solution,
  //   .future-predictions,
  //   .algae-monitoring,
  //   .historical-monitoring-data {
  //     padding: 12px 8px;
  //     border-radius: 8px;
  //   }

  //   .center-image {
  //     min-height: 200px;
  //   }
  // }
</style>

<style lang="less" scoped></style>
