<!-- 详情弹框组件 -->
<template>
  <div class="detail-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="detail-dialog" @click.stop>
      <div class="dialog-header">
        <h2 class="dialog-title">{{
          props.dataType === 'historical' ? `历史${title}-趋势图` : title
        }}</h2>
        <div class="dialog-controls">
          <button @click="closeDialog" class="close-btn">✕</button>
        </div>
      </div>
      <div class="dialog-content">
        <div class="time-selector">
          <template v-if="props.dataType === 'historical' || props.dataType === 'alertWarning'">
            <div class="picker-before">选择日期：</div>
            <div style="width: 320px">
              <RangePicker
                @change="handleDateChange"
                style="height: 40px; border-radius: 8px 8px 8px 8px; border: 1px solid #d9d9d9"
                v-model:value="value1"
                :allowClear="false"
              />
            </div>
          </template>
          <template v-else>
            <div class="btn-box">
              <div
                class="btn-radio"
                :class="{ active: activePeriod === item.value }"
                v-for="item in timePeriods"
                :key="item.value"
                @click="handleTime(item.value)"
                ><span>{{ item.label }}</span></div
              >
            </div>
          </template>
          <div style="margin-left: 16px" v-if="props.dataType === 'historical'">
            <Button
              @click="exportData"
              :icon="h(Icon, { icon: 'icon-park-outline:upload' })"
              style="
                height: 40px;
                font-size: 18px;
                border-radius: 8px 8px 8px 8px;
                border: 1px solid #d9d9d9;
              "
              class="export-btn"
              >导出</Button
            >
          </div>
        </div>
        <div
          v-if="props.dataType !== 'alertWarning' && !EmptyFlag"
          class="chart-container"
          ref="chartContainer"
        ></div>
        <Empty v-if="props.dataType !== 'alertWarning' && EmptyFlag" />
        <BasicTable
          v-if="props.dataType === 'alertWarning'"
          :dataSource="alertTableData"
          :columns="alertTableColumns"
          :pagination="alertPagination"
          :showIndexColumn="true"
          @change="handleChangePage"
        >
          <template #tableAction="{ column, record }">
            <div
              v-if="column.key === 'realTimeDensity' || column.key === 'totalNumber'"
              :style="{
                color: record[column.key] > 0 ? 'red' : 'none',
              }"
            >
              {{ record[column.key] }}
            </div>
            <div v-else>
              {{ record[column.key] }}
            </div>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { h, ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs';
  import type { Dayjs } from 'dayjs';
  import { BasicTable } from '/@/components/Table';
  import { Icon } from '/@/components/Icon';
  import { RangePicker, Button } from 'ant-design-vue';
  import {
    waterMonitorList, // 主页指标点击其他指标返回的柱状图接口
    main2MibPredictList, // 点击未来预测数据返回柱状图接口
    pageWarnRecord, // 告警记录列表分页
    export2Mib,
  } from '/@zhcz/api/mib-early-warning';
  import { downloadByData } from '/@/utils/file/download';
  import { getScaleValByClientWidth } from '/@zhcz/views/overview/utils';
  import Empty from './Empty.vue';
  import { colorRgba } from './data';
  // Props
  interface Props {
    visible: boolean;
    title: string;
    dataType: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    title: '监测数据',
    dataType: '',
  });

  type RangeValue = [Dayjs, Dayjs];
  const value1 = ref<RangeValue>([dayjs().subtract(6, 'day'), dayjs()]);

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    close: [];
  }>();
  const EmptyFlag = ref<boolean>(false);
  const alertTableData = ref<any[]>([]);
  const alertTableColumns = ref([
    // {
    //   title: '序号',
    //   dataIndex: 'filterName',
    // },
    {
      title: '时间',
      dataIndex: 'warnTime',
    },
    {
      title: '内容',
      dataIndex: 'warnDesc',
    },
  ]);
  const alertPagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
  });
  const chartContainer = ref<HTMLElement>();
  let chartInstance: echarts.ECharts | null = null;

  // 图表数据类型定义
  interface ChartData {
    dates: string[];
    lineData1: any[];
    barData1: any[];
    lineData2?: any[];
    barData2?: any[];
  }
  const chartsUnit = ref<string>('mg/L');
  // 模拟数据
  const chartData = ref<ChartData>({
    dates: [],
    lineData1: [],
    barData1: [],
    lineData2: [],
    barData2: [],
  });
  const indexNameBox = ref<string[]>([]);
  const seriesData = ref<any[]>([]);
  const activePeriod = ref('6');
  const timePeriods = ref([
    { label: '今日', value: '0' },
    { label: '近一周', value: '6' },
    { label: '近一月', value: '30' },
  ]);
  const handleChangePage = (data) => {
    alertPagination.value.current = data.current;
    alertPagination.value.pageSize = data.pageSize;
    fetchData();
  };
  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return;
    chartInstance = echarts.init(chartContainer.value);
    const option = {
      // color: ['#65A7F6', '#65A7F6', '#F4B478', '#F4B478'],
      title: {
        show: false,
        text: `历史${props.title}`,
        left: 20,
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 600,
          color: '#333',
        },
      },
      tooltip: {
        trigger: 'axis',
        appendTo: () => document.body,
        renderMode: 'html',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#333',
          fontSize: 18,
          lineHeight: 28,
          height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        formatter: (params) => {
          const htmlStrBefor = `<div style="">${params[0].name}</div>`;
          let filterData = params.filter((item) => item.seriesType === 'bar');
          const htmlStrAfter = filterData
            .map((item) => {
              return `${item.marker} <span style="display: inline-block;">${
                item.seriesName
              }</span><span style="display: inline-block; margin-left: 20px; text-align: right;">${
                item.value || item.value == 0 ? Number(item.value) : '-'
              } </span>`;
              // &nbsp;${props.dataList.length ? props.dataList[index].unitName : ''}
            })
            .join('<br/>');
          return htmlStrBefor + htmlStrAfter;
        },
      },
      legend: {
        data: [],
        bottom: 0,
        right: 'center',
        icon: 'circle',
        textStyle: {
          fontSize: 18,
          color: '#000',
          // padding: [3, 5]
        },
      },
      grid: {
        left: '0',
        right: '0',
        bottom: '60',
        top: '16%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: chartData.value.dates,
          axisPointer: {
            type: 'shadow',
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#B3B1B1',
              type: 'solid',
            },
          },
          axisLabel: {
            // formatter: '{value}',
            textStyle: {
              color: '#000',
              fontSize: 16,
              lineHeight: 24,
              height: 24,
              fontWeight: 400,
            },
            interval: 'auto',
            rotate: 0,
            margin: 8,
          },
          axisTick: {
            show: false,
          },
        },
      ],
      yAxis: {
        type: 'value',
        fontSize: 18,
        // name: `${chartsUnit.value}`,
        // name: `单位（${chartsUnit.value}）`,
        nameTextStyle: {
          align: 'left',
          fontSize: 18,
          // padding: [0, 0, 0, getScaleValByClientWidth(-30)],
          color: '#000',
        },
        // nameLocation: 'middle',
        // nameGap: 45,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#B3B1B1',
            type: 'dashed',
          },
        },
        axisLabel: {
          formatter: '{value}',
          textStyle: {
            color: '#000',
            fontSize: 18,
            lineHeight: 28,
            height: 28,
            fontWeight: 400,
          },
        },
      },
      series: [],
    };
    chartInstance.setOption(option as any);
    chartInstance.on('legendselectchanged', function (event) {
      const selectedBox: string[] = [];
      Object.keys(event.selected).forEach((item) => {
        if (event.selected[item]) {
          selectedBox.push(item);
        }
      });
      const filterName = indexNameBox.value.filter((item) => selectedBox.includes(item));
      const filterSeries: any = seriesData.value.map((item) => {
        if (filterName.includes(item.name) || filterName.includes(item.name.split('趋势')[0])) {
          return item;
        } else {
          return {
            ...item,
            data: [],
          };
        }
      });
      chartInstance?.setOption({
        series: filterSeries,
        legend: {
          selected: event.selected,
        },
      });
    });
  };

  // 处理日期变化
  const handleDateChange = () => {
    // 调用API获取新数据
    fetchData();
  };

  // 获取数据类型映射
  // const getMonitorType = (dataType: string): number => {
  //   const typeMap: Record<string, number> = {
  //     ph: 1,
  //     temperature: 2,
  //     chloramine: 3,
  //     turbidity: 4,
  //     chlorophyll: 5,
  //     '2-MIB': 6,
  //   };
  //   return typeMap[dataType] || 1;
  // };

  // 获取数据
  const fetchData = async () => {
    try {
      EmptyFlag.value = false;
      const params: {
        startDate: string;
        endDate: string;
        deviceId: string;
        monitorType?: number;
        pageNum?: number;
        pageSize?: number;
      } = {
        startDate: dayjs(value1.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(value1.value[1]).format('YYYY-MM-DD'),
        deviceId: '002',
        monitorType: Number(props.dataType),
      };
      // console.log('获取数据:', params);
      let api = waterMonitorList;
      if (props.dataType === 'historical') {
        api = main2MibPredictList;
        delete params.monitorType;
      } else if (props.dataType === 'alertWarning') {
        params.pageNum = alertPagination.value.current;
        params.pageSize = alertPagination.value.pageSize;
        api = pageWarnRecord;
      } else {
        params.startDate = dayjs().subtract(Number(activePeriod.value), 'day').format('YYYY-MM-DD');
        params.endDate = dayjs().format('YYYY-MM-DD');
      }
      const response: any = await api(params);
      if (response) {
        // 处理API返回的数据
        let gridData: any = {};
        let Ymax = 0;
        seriesData.value = [];
        let dates: any = [];
        indexNameBox.value = [];
        if (props.dataType === 'alertWarning') {
          // 表格数据
          alertTableData.value = response.records || [];
          alertPagination.value.total = response.total || 1;
        } else {
          // echarts图
          let values: any = [];
          if (props.dataType === 'historical') {
            // 历史监测数据
            dates = response[0].data.map((item) => dayjs(item.date).format('DD') + '日');
          } else {
            // 指标点 柱状图 数据
            dates =
              activePeriod.value === '0'
                ? response[0].data.map((item) => item.date.substring(0, 2) + '时')
                : response[0].data.map((item) => dayjs(item.date).format('DD') + '日');
            gridData = {
              // left: '0',
              // right: '0',
              bottom: '0',
              // top: '16%',
              containLabel: true,
            };
          }
          response.forEach((item, index) => {
            values.push(item.data.map((val) => val.value));
            seriesData.value.push({
              data: item.data.map((val) => val.value),
              name: `${item.indexName}趋势`,
              type: 'line',
              smooth: false,
              symbol: 'none',
              itemStyle: {
                color: colorRgba[index] || 'none',
              },
            });
            seriesData.value.push({
              data: item.data.map((val) => val.value),
              name: item.indexName,
              type: 'bar',
              barWidth: 20,
              itemStyle: {
                color: colorRgba[index] || 'none',
              },
            });
            indexNameBox.value.push(item.indexName);
          });
          chartsUnit.value = response[0].unitName || 'mg/L';
          console.log('chartsUnit.value', chartsUnit.value);
          // 获取Y轴最大值
          Ymax = Math.max.apply(null, values.flat());
        }
        // 更新图表
        if (chartInstance && props.dataType !== 'alertWarning') {
          // console.log('yAxis-response.unitName', response.unitName);
          chartInstance.setOption({
            xAxis: {
              data: dates,
            },
            series: seriesData.value,
            legend: {
              data: props.dataType === 'historical' ? indexNameBox.value : [],
              bottom: 10,
            },
            yAxis: {
              name:
                chartsUnit.value && chartsUnit.value !== ' ' ? `单位（${chartsUnit.value}）` : '',
              nameLocation: 'end',
              // nameGap: 45,
              nameTextStyle: {
                fontSize: 18,
                padding: [
                  0,
                  0,
                  20,
                  Ymax > 9.99 ? getScaleValByClientWidth(-30) : getScaleValByClientWidth(-19),
                ],
                color: '#000',
              },
            },
            grid: {
              ...gridData,
            },
          });
        }
      } else {
        EmptyFlag.value = true;
      }
    } catch (error) {
      EmptyFlag.value = true;
      console.error('获取数据失败:', error);
    }
  };

  const handleTime = (value: string) => {
    activePeriod.value = value;
    fetchData();
  };
  // 导出数据
  const exportData = async () => {
    try {
      const res = await export2Mib({
        startDate: dayjs(value1.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(value1.value[1]).format('YYYY-MM-DD'),
        deviceId: '002',
      });
      const fileName = `历史${props.title}-趋势图.xlsx`;
      downloadByData(res, fileName);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 关闭弹框
  const closeDialog = () => {
    emit('update:visible', false);
    value1.value = [dayjs().subtract(6, 'day'), dayjs()];
    activePeriod.value = '6';
    emit('close');
  };

  // 处理遮罩层点击
  const handleOverlayClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeDialog();
    }
  };

  // 监听visible变化
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        nextTick(() => {
          initChart();
          fetchData();
        });
      }
    },
  );

  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
    }
    window.removeEventListener('resize', handleResize);
  });
</script>

<style lang="less" scoped>
  // @media (max-width: 1080px) {
  //   .detail-dialog {
  //     width: 95%;
  //     height: 90%;
  //     margin: 20px;
  //   }
  // }

  .detail-dialog-overlay {
    overflow: hidden;
    position: absolute;
    // top: 0;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    // align-items: center;
    z-index: 99;
    transform: translateX(-50%);
    padding-top: 30%;
  }

  .detail-dialog {
    background: white;
    border-radius: 8px;
    width: calc(100% - 48px);
    max-width: 1000px;
    height: 80%;
    max-height: 520px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .dialog-header {
    padding: 20px 24px;
    // border-bottom: 1px solid #e5e7eb;
    display: flex;
    height: 64px;
    justify-content: space-between;
    align-items: center;

    .dialog-title {
      margin: 0;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 24px;
      color: #000000;
    }

    .dialog-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .export-btn {
        padding: 8px 16px;
        background-color: #3b82f6;
        border: none;
        border-radius: 6px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: #2563eb;
        }
      }

      .close-btn {
        // padding: 8px 12px;
        // background-color: #f3f4f6;
        color: #6b7280;
        border: none;
        // border-radius: 6px;
        font-size: 20px;
        line-height: 20px;
        padding: 8px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: #e5e7eb;
        }
      }
    }
  }

  .dialog-content {
    flex: 1;
    padding: 12px 24px;
    overflow: hidden;
    position: relative;

    .time-selector {
      width: auto;
      position: absolute;
      top: 12px;
      right: 0;
      display: flex;
      align-items: center;
      margin-right: 24px;
      z-index: 100;
      // gap: 8px;

      .btn-box {
        // position: absolute;
        height: 40px;
        // top: 0;
        // right: 0;
        border-radius: 8px;
        cursor: pointer;
        display: flex;

        .btn-radio {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #ffffff;
          line-height: 40px;
          width: 86px;
          height: 40px;
          border: 1px solid #d9d9d9;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;

          &:hover {
            background: #f0f0f0;
          }

          &.active {
            transition: all 0.3s;
            background: #0b62cb;
            border: 1px solid #0b62cb;
            color: #fff;
          }

          &:first-child {
            border-radius: 8px 0 0 8px;
            border-right: none;
          }

          &:last-child {
            border-radius: 0px 8px 8px 0;
            border-left: none;
          }
        }
      }

      :deep(.ant-picker) {
        & .ant-picker-input > input {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
          line-height: 18px;
        }
      }

      .picker-before {
        width: 90px;
        height: 40px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        line-height: 40px;
      }

      // .export-btn {
      // }
    }

    :deep(.vben-basic-table) {
      margin-top: 56px;
      height: calc(100% - 56px);
      overflow: auto;

      .ant-table-cell {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
      }

      .ant-table-wrapper {
        padding: 0;
        margin: 0;
        height: 100%;
      }

      .ant-table {
        height: 100%;
      }

      .ant-table-container {
        height: calc(100% - 55px);
      }

      .ant-table-body {
        overflow-y: auto;
        height: 100%;
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 400px;
  }
</style>
