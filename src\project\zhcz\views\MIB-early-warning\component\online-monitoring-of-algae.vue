<!-- 藻类在线监测 -->
<template>
  <div class="algae-monitoring">
    <HLCard
      className="custom-card"
      :styleData="{ borderRadius: '16px' }"
      themeColor="Light"
      :line="true"
    >
      <template #headerLeftAfter>
        <div class="text__header" @click="goto('algae')"
          >藻类在线监测 <span class="unit_text">(单位：{{ titleData.unit }})</span></div
        >
        <img
          class="text__next"
          src="../assets/images/detail-goto.png"
          @click="goto('algae')"
          alt=""
          srcset=""
        />
      </template>
      <template #headerRight>
        <div class="monitoring-point">
          <!-- <span class="point-icon">📍</span> -->
          <img
            src="../assets/images/location.png"
            style="width: 32px; margin-right: 12px"
            alt=""
            srcset=""
          />
          监测点：{{ titleData.address }}
        </div>
      </template>

      <template #defaultBody>
        <div class="algae-monitoring__content">
          <div
            v-for="(item, index) in monitoringData"
            :style="{ backgroundImage: `url(${bgImages[index]})`, backgroundSize: '100% 100%' }"
            :key="index"
            class="monitoring-item"
          >
            <div class="monitoring-circle">
              <div class="circle-value">{{ item.val || '--' }}</div>
              <div class="circle-label">{{ item.name || '--' }}</div>
            </div>
            <div class="monitoring-details">
              <div
                class="detail-item"
                v-for="(detail, detailIndex) in item.percentages"
                :key="detailIndex"
              >
                <span class="detail-label">{{ detail.name || '--' }}</span>
                <span class="detail-value">{{ detail.val || '--' }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import leftCardBg from '../assets/images/left-card-bg.png';
  import centerCardBg from '../assets/images/center-card-bg.png';
  import rightCardBg from '../assets/images/right-card-bg.png';
  import { algaeOnlineMonitoring } from '/@zhcz/api/mib-early-warning';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  const titleData = ref({
    address: '钢铁水库',
    unit: 'ind/L',
  });
  const bgImages = [leftCardBg, centerCardBg, rightCardBg];
  const monitoringData = ref([
    {
      val: 108,
      name: '总藻',
      percentages: [
        { name: '硅藻', val: '--' },
        { name: '蓝藻', val: '--' },
      ],
    },
    {
      val: 96,
      name: '硅藻',
      percentages: [
        { name: '小环藻', val: '--' },
        { name: '羽纹藻', val: '--' },
      ],
    },
    {
      val: 94,
      name: '致嗅藻',
      percentages: [
        { name: '伪鱼腥藻', val: '--' },
        { name: '浮丝藻', val: '--' },
      ],
    },
  ]);
  const goto = (path: string) => {
    console.log(path);
    router.push({ path: `mib/${path}` });
  };
  const getData = async () => {
    const res = await algaeOnlineMonitoring({});
    // console.log('online-monitoring-of-algae', res);
    if (res && res.data.length) {
      titleData.value.address = res.address;
      titleData.value.unit = res.unit;
      monitoringData.value = res.data.map((item: any) => ({
        val: item.val,
        name: item.name,
        percentages: item.percentages.map((p: any) => ({
          name: p.name,
          val: p.val,
        })),
      }));
      // centerData.value = res.map((item: any) => ({
      //   warnTime: '',
      //   warnDesc: item.warnDesc,
      //   warnType: item.warnType || 0,
      // }));
    }
  };
  onMounted(() => {
    getData();
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        getData();
      }
    },
  );
</script>
<style lang="less" scoped>
  .algae-monitoring {
    width: 100%;
    // background: rgba(255, 255, 255, 0.95);
    // border-radius: 16px;
    // padding: 24px 32px;
    // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    // backdrop-filter: blur(10px);

    .custom-card {
      border-radius: 16px;
      border: 2px solid #ffffff;
      background: rgba(247, 250, 254, 1);

      :deep(.base-header) {
        padding: 16px 24px;
        height: 56px;
      }

      :deep(.base-header_line) {
        border-bottom: 1px solid #bcc5ce;
      }

      :deep(.card-body) {
        padding: 20px 24px;
      }

      .text__header {
        // display: inline-block;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 24px;
        line-height: 24px;
        color: #000000;
        position: relative;
        cursor: pointer;
      }

      .text__next {
        height: 32px;
        cursor: pointer;
      }
    }
    // @media (max-width: 1200px) {
    //   padding: 16px 20px;

    //   .algae-monitoring__header {
    //     font-size: 18px;
    //     padding-left: 16px;
    //     // flex-direction: column;
    //     align-items: flex-start;
    //     gap: 8px;

    //     .monitoring-point {
    //       font-size: 14px;
    //     }
    //   }

    //   .monitoring-circle {
    //     width: 100px;
    //     height: 100px;

    //     .circle-value {
    //       font-size: 24px;
    //     }

    //     .circle-label {
    //       font-size: 14px;
    //     }
    //   }

    //   .monitoring-details {
    //     min-width: 100px;

    //     .detail-item {
    //       padding: 4px 8px;

    //       .detail-label,
    //       .detail-value {
    //         font-size: 12px;
    //       }
    //     }
    //   }
    // }

    .monitoring-point {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 22px;
      color: #000000;
      // display: inline-block;
    }

    .algae-monitoring__content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      // gap: 32px;

      // @media (max-width: 1200px) {
      //   // flex-direction: column;
      //   gap: 20px;
      // }
    }

    .monitoring-item {
      width: 306px;
      // height: 176px;
      // flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 16px 20px;
    }

    .monitoring-circle {
      // background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;
      // box-shadow: 0 8px 24px rgba(33, 150, 243, 0.15);
      // border: 3px solid rgba(33, 150, 243, 0.2);

      .circle-value {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 44px;
        color: #000000;
        line-height: 44px;
        margin-bottom: 10px;
      }

      .circle-label {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 22px;
        line-height: 22px;
        color: #000000;
      }
    }

    .monitoring-details {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px 0;

      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .detail-label {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
          line-height: 18px;
        }

        .detail-value {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
          line-height: 18px;
        }
      }
    }
  }
</style>
