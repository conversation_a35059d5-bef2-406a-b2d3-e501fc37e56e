<!-- 预警条 -->
<template>
  <div class="message_alarm">
    <div class="message_content">
      <div
        class="message_alarm_item"
        v-for="(item, index) in setDate"
        :style="{ background: color[item.warnType] }"
        :key="index"
        @click="
          openDetail({
            indexCode: 'alertWarning',
            indexName: '消息列表',
          })
        "
      >
        <img :src="imgSrcs[item.warnType]" class="message-img" alt="" srcset="" />
        <span class="message-txt">{{ item.warnTime }}{{ item.warnDesc }}</span>
        <img src="../assets/images/msg-goto.png" alt="" srcset="" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import imgSrc from '../assets/images/alert-orange.png';
  import imgSrcR from '../assets/images/alert-red.png';
  import imgSrcY from '../assets/images/alert-yellow.png';
  import { getLastRecord } from '/@zhcz/api/mib-early-warning';
  import { useIntervalFn } from '@vueuse/core';
  import dayjs from 'dayjs';
  const color = ['#FFE3E3', '#FEE9D9', '#FCF7D5'];
  const imgSrcs = [imgSrcR, imgSrc, imgSrcY];
  const dataIndex = ref(0);
  interface MessageAlarm {
    warnTime: string;
    warnDesc: string;
    warnType: number;
  }
  const emit = defineEmits(['openDialog']);
  const openDetail = (item: any) => {
    // if (type === '2-MIB') {
    //   return;
    // }
    emit('openDialog', item);
  };
  const messageAlarmData = ref<MessageAlarm[]>([
    {
      warnTime: dayjs('2021-10-10 10:21:02').format('HH:mm:ss'),
      warnDesc: '叶绿素a浓度超出10mg/L，具有爆发风险',
      // icon: imgSrc,
      // color: color[0],
      warnType: 1, // 1-红色预警 2-橙色预警 3-黄色预警
    },
    // {
    //   time: '10:21:02',
    //   content: '叶绿素a浓度超出10mg/L，具有爆发风险',
    //   icon: imgSrcR,
    //   color: color[1],
    // },
    // {
    //   time: '10:21:02',
    //   content: '叶绿素a浓度超出10mg/L，具有爆发风险',
    //   icon: imgSrcY,
    //   color: color[2],
    // },
  ]);
  const setDate = computed(() => [messageAlarmData.value[dataIndex.value]]);
  const setIndex = () => {
    dataIndex.value += 1;
    if (dataIndex.value >= messageAlarmData.value.length) {
      dataIndex.value = 0;
    }
  };
  const getData = async () => {
    const res = await getLastRecord({});
    // console.log('message_alarm', res);
    if (res.length) {
      messageAlarmData.value = res.map((item: any) => ({
        warnTime: dayjs(item.warnTime).format('HH:mm:ss'),
        warnDesc: item.warnDesc,
        warnType: item.warnType || 0,
      }));
    }
  };
  const { pause } = useIntervalFn(setIndex, 2 * 1000);
  onMounted(() => {
    pause();
    getData();
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        dataIndex.value = 0;
        getData();
      }
    },
  );
</script>

<style lang="less" scoped>
  .message_alarm {
    width: 100%;

    .message_content {
      margin: 20px 32px 47px;
      height: 48px;
      // background: #fee9d9;
      border-radius: 4px 4px 4px 4px;
      border: 2px solid #ffffff;
      overflow: hidden;
      cursor: pointer;

      .message_alarm_item {
        padding: 0 16px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        line-height: 44px;
        text-align: left;
        display: flex;
        align-items: center;
        gap: 0 8px;
        cursor: pointer;

        .message-txt {
          flex: 1;
          text-align: left;
        }

        .message-img {
          width: 35px;
          // height: 22px;
          vertical-align: middle;
        }
      }
    }
  }
</style>
