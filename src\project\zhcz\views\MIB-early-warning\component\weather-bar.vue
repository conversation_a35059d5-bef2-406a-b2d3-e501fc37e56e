<!-- 顶部天气栏 -->
<template>
  <div class="weather-bar">
    <div class="weather-bar__left">
      <!-- <img class="weather-icon" :src="weatherIcon" /> -->
      <div class="icon-wrapper" v-if="info.icon">
        <i :class="`qi-${info.icon}`"></i>
      </div>
      <span class="weather-bar__temp">{{ info.temp !== '' ? info.temp : '-' }}℃</span>
      <div class="weather-bar__text font18">{{ info.text }}</div>
      <img class="weather-bar__icon" :src="rainwatherImg" />
      湿度
      <span class="font18">
        <span class="font-weight">{{ info.humidity !== '' ? `${info.humidity}` : '-' }}</span
        >%</span
      >
      <img class="weather-bar__icon" :src="atmosphericPressureImg" />
      气压
      <span class="font18">
        <span class="font-weight">{{ info.pressure !== '' ? `${info.pressure}` : '-' }}</span
        >kPa</span
      >
      <img class="weather-bar__icon" :src="windPowerImg" />
      风力
      <span class="font18"
        ><span class="font-weight">{{ info.windScale !== '' ? `${info.windScale}` : '-' }}</span
        >级</span
      >
    </div>
    <div class="weather-bar__right">
      <span class="weather-bar__time font-weight">{{ currentTime }}</span>
      <div
        class="weather-bar__alarm"
        @click="
          openDetail({
            indexCode: 'alertWarning',
            indexName: '消息列表',
          })
        "
      >
        <img class="alarm_box" :src="alarmImg" alt="" srcset="" />
        <span class="alarm_size" v-if="total">{{ total > 99 ? '99⁺' : total }}</span>
      </div>
      <Popover trigger="click" v-model:open="visible">
        <template #content>
          <div class="tips-box" style="cursor: pointer" @click="toggleClick()">
            <img
              class="bar_popover-img"
              style="height: 20px; margin-right: 8px; margin-left: 4px; vertical-align: sub"
              :src="quit"
              v-if="toggle"
              alt=""
              srcset=""
            />
            <span
              style="font-family: PingFang SC; font-weight: 600; font-size: 16px; color: #333333"
              >{{ toggle ? '退出全屏' : '进入全屏' }}</span
            >
          </div>
        </template>
        <div class="weather-bar__set">
          <img class="set_box" :src="setting" alt="" srcset="" />
        </div>
      </Popover>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  // import weatherIcon from '../assets/images/weather.png';
  import rainwatherImg from '../assets/images/rainwather.png';
  import atmosphericPressureImg from '../assets/images/atmospheric-pressure.png';
  import windPowerImg from '../assets/images/wind-power.png';
  import alarmImg from '../assets/images/alarm.png';
  import setting from '../assets/images/setting.png';
  import quit from '../assets/images/quit.png';
  // import { Icon } from '/@/components/Icon';
  import {
    getWeather24HApi,
    pageWarnRecord, // 告警记录列表分页
  } from '/@zhcz/api/mib-early-warning';
  import { Popover } from 'ant-design-vue';
  import dayjs from 'dayjs';

  const currentTime = ref('');
  // const weather = ref('阵雨转晴');
  // const temperature = ref('25');
  // const humidity = ref('75');
  // const pressure = ref('101');
  // const windLevel = ref('4');
  const info = ref({
    text: '阵雨转晴',
    icon: '315',
    temp: '26',
    windScale: '3',
    humidity: '75',
    pressure: '101',
  });

  // const textTransformType = (text) => {
  //   if (!text) return 0;
  //   return text.includes('晴')
  //     ? 1
  //     : text.includes('雨')
  //     ? 2
  //     : text.includes('多云')
  //     ? 3
  //     : text.includes('阴')
  //     ? 4
  //     : 0;
  // };

  const getWeather = async () => {
    try {
      const { data } = await getWeather24HApi({});
      const HOUR = 'HH';
      const findItem = JSON.parse(data)?.hourly?.find(
        (i) => dayjs(i.fxTime).format(HOUR) === dayjs().format(HOUR),
      );

      info.value = {
        text: findItem?.text ?? '',
        // text: '大暴雨到特大暴雨',
        icon: findItem?.icon ?? '',
        temp: findItem?.temp ?? '',
        windScale: findItem?.windScale ?? '',
        humidity: findItem?.humidity ?? '',
        pressure: findItem?.pressure ?? '',
      };
    } catch (error) {
      throw error;
    }
  };
  const visible = ref<boolean>(false);

  const emit = defineEmits(['openDialog']);
  const openDetail = (item: any) => {
    // if (type === '2-MIB') {
    //   return;
    // }
    emit('openDialog', item);
  };

  const toggle = ref<boolean>(false);

  // 检查当前是否处于全屏状态
  const isFullscreen = (): boolean => {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  };

  // 更新全屏状态
  const updateFullscreenState = () => {
    const currentFullscreenState = isFullscreen();
    if (toggle.value !== currentFullscreenState) {
      toggle.value = currentFullscreenState;
    }
  };

  // 全屏状态变化监听器
  const handleFullscreenChange = () => {
    updateFullscreenState();
  };

  // 键盘事件监听器
  const handleKeydown = (event: KeyboardEvent) => {
    // F11 键处理
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
    // ESC 键处理
    if (event.key === 'Escape' && isFullscreen()) {
      // ESC 键会自动退出全屏，我们只需要更新状态
      setTimeout(updateFullscreenState, 100);
    }
  };

  // 切换全屏状态
  const toggleFullscreen = async () => {
    try {
      if (isFullscreen()) {
        await exitScreen();
      } else {
        await fullScreen();
      }
    } catch (error) {
      console.warn('全屏操作失败:', error);
    }
  };

  const toggleClick = () => {
    visible.value = false;
    toggleFullscreen();
  };

  // 进入全屏
  function fullScreen(): Promise<void> {
    return new Promise((resolve, reject) => {
      const el = document.documentElement as any;
      const rfs =
        el.requestFullscreen ||
        el.webkitRequestFullScreen ||
        el.mozRequestFullScreen ||
        el.msRequestFullscreen;

      if (typeof rfs !== 'undefined' && rfs) {
        const promise = rfs.call(el);
        if (promise && promise.then) {
          promise.then(resolve).catch(reject);
        } else {
          // 对于不返回 Promise 的浏览器，延迟一下再 resolve
          setTimeout(resolve, 100);
        }
      } else {
        reject(new Error('浏览器不支持全屏API'));
      }
    });
  }

  // 退出全屏
  function exitScreen(): Promise<void> {
    return new Promise((resolve, reject) => {
      const doc = document as any;
      const exitFullscreen =
        doc.exitFullscreen ||
        doc.webkitCancelFullScreen ||
        doc.webkitExitFullscreen ||
        doc.mozCancelFullScreen ||
        doc.msExitFullscreen;

      if (typeof exitFullscreen !== 'undefined' && exitFullscreen) {
        const promise = exitFullscreen.call(doc);
        if (promise && promise.then) {
          promise.then(resolve).catch(reject);
        } else {
          // 对于不返回 Promise 的浏览器，延迟一下再 resolve
          setTimeout(resolve, 100);
        }
      } else {
        reject(new Error('浏览器不支持退出全屏API'));
      }
    });
  }
  const total = ref<number>(0);
  const getData = async () => {
    const res = await pageWarnRecord({
      endDate: dayjs().format('YYYY-MM-DD'),
      startDate: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
      pageNum: 1,
      pageSize: 10,
      deviceId: '002',
    });
    total.value = res.total || 0;
  };

  function updateTime() {
    const now = new Date();
    currentTime.value = now.toLocaleString('zh-CN', { hour12: false });
  }

  // 时间更新定时器
  let timeInterval: NodeJS.Timeout;

  onMounted(() => {
    // 初始化时间显示
    updateTime();
    getWeather();
    timeInterval = setInterval(updateTime, 1000);

    // 添加全屏状态变化监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeydown);

    // 初始化全屏状态
    updateFullscreenState();
  });

  onUnmounted(() => {
    // 清理定时器
    if (timeInterval) {
      clearInterval(timeInterval);
    }

    // 移除全屏状态变化监听器
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

    // 移除键盘事件监听器
    document.removeEventListener('keydown', handleKeydown);
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  getData();
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        getData();
      }
    },
  );
</script>

<style lang="less" scoped>
  @import '../assets/css/font.less';
  @import '../assets/qweather-icons/qweather-icons.less';

  .weather-bar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(241, 249, 251, 1);
    padding: 0 32px;
    height: 72px;
    font-size: 18px;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .weather-bar__left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    // .weather-icon {
    //   width: 42px;
    //   height: 35px;
    // }
    .icon-wrapper {
      // .font-size(36);
      font-size: 36px;
    }
    // .font-size(@font-size) {
    //   @min-design-width: 1440;
    //   @min-font-size: 12;

    //   font-size: (@font-size / @design-width * 100vw);

    //   @media screen and (max-width: unit(@min-design-width, px)) {
    //     font-size: unit(@min-font-size, px);
    //   }
    // }
    .weather-bar__text {
      display: inline-block;
      max-width: 100px;
    }

    .font18 {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
    }

    .weather-bar__icon {
      width: 18px;
      height: 18px;
      margin-right: 4px;
      margin-left: 24px;
    }

    .weather-bar__temp {
      font-family: Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 22px;
      color: #000000;
    }

    .font-weight {
      font-family: Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 20px;
      color: #000000;
      line-height: 20px;
    }

    // .weather-bar__time {
    //   margin-right: 20px;
    // }

    .weather-bar__right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .weather-bar__alarm {
      position: relative;
      cursor: pointer;

      .alarm_box {
        width: 48px;
        height: 48px;
      }

      .alarm_size {
        position: absolute;
        top: 0;
        right: 0;
        height: 24px;
        width: 24px;
        background: #f55400;
        border-radius: 12px;
        color: #fff;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
      }
    }

    .weather-bar__set {
      cursor: pointer;
      width: 40px;
      height: 40px;
      background: #ffffff;
      box-shadow: 0px 0px 8px 0px #d5e5ee, inset 4px -4px 8px 0px #b8d8e5;
      border-radius: 8px 8px 8px 8px;
      border: 2px solid #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;

      & > img {
        height: 24px;
        width: 24px;
      }
    }
  }
</style>
