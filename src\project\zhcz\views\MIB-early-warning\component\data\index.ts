// 类型映射
export const typeMapping: Record<string, string> = {
  chloramine: '游离氯',
  temperature: '温度',
  turbidity: '浊度',
  ph: 'pH',
  chlorophyll: '叶绿体',
  '2-MIB': '2-MIB',
  historical: '2-MIB',
  alertWarning: '消息列表',
};

export const colorRgba: string[] = [
  '#65A7F6',
  '#F4B478',
  'rgba(31,195,164,0.8)',
  'rgba(97,195,31,0.8)',
  'rgba(78,96,253,0.8)',
  'rgba(45,48,254,0.8)',
  'rgba(115,45,254,0.8)',
  'rgba(31,195,105,0.8)',
  'rgba(254,139,45,0.8)',
  'rgba(254,197,45,0.8)',
  'rgba(254,244,45,0.8)',
  'rgba(45,202,254,0.8)',
  'rgba(45,226,254,0.8)',
  'rgba(84,112,198,0.8)',
  'rgba(145,204,117,0.8)',
  'rgba(250,200,88,0.8)',
  'rgba(238,102,102,0.8)',
  'rgba(115,192,222,0.8)',
  'rgba(59,162,114,0.8)',
  'rgba(252,132,82,0.8)',
  'rgba(154,96,180,0.8)',
  'rgba(234,124,204,0.8)',
];
