<!-- 历史监测数据 -->
<template>
  <div class="historical-monitoring-data">
    <HLCard
      className="custom-card"
      :styleData="{ borderRadius: '16px' }"
      themeColor="Light"
      :line="true"
    >
      <template #headerLeftAfter>
        <div class="text__header" @click="goto('data')">监测数据 </div>
        <img
          class="text__next"
          @click="goto('data')"
          src="../assets/images/detail-goto.png"
          alt=""
          srcset=""
        />
      </template>
      <template #headerRight>
        <!-- <div class="monitoring-point">
          <img
            src="../assets/images/location.png"
            style="width: 32px; margin-right: 12px"
            alt=""
            srcset=""
          />
          监测点：钢铁水库
        </div> -->
      </template>

      <template #defaultBody>
        <div class="charts-box">
          <div ref="barCharts" v-if="!EmptyFlag" class="bar-charts"></div>
          <Empty v-if="EmptyFlag" />
          <div class="btn-box">
            <div
              class="btn-radio"
              :class="{ active: activePeriod === item.value }"
              v-for="item in timePeriods"
              :key="item.value"
              @click="handleTime(item.value)"
              ><span>{{ item.label }}</span></div
            >
          </div>
        </div>
      </template>
    </HLCard>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, Ref, watch } from 'vue';
  import { historyMonitoringChartData } from '/@zhcz/api/mib-early-warning';
  import dayjs from 'dayjs';

  import { useECharts } from '/@/hooks/web/useECharts';
  // import { BarChart } from 'echarts/charts';
  import { HLCard } from '/@/project/zhcz/components/HLCard';
  import { getScaleValByClientWidth } from '/@zhcz/views/overview/utils';
  import Empty from './Empty.vue';
  import { useRouter } from 'vue-router';
  // import { HLCard } from 'lutwelve-components';
  // import 'lutwelve-components/dist/style.css';

  const router = useRouter();
  const EmptyFlag = ref<boolean>(false);
  const barCharts = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(barCharts as Ref<HTMLDivElement>);
  const activePeriod = ref('6');
  const timePeriods = ref([
    { label: '近一周', value: '6' },
    { label: '近一月', value: '30' },
  ]);
  const alertWarnLevel = ref('0');
  const chartData = ref([
    {
      indexName: '2-MIB',
      type: 'bar',
      seriesLabel: false,
      data: [
        {
          collectDateTime: '2025-08-07',
          value: null,
        },
      ],
    },
  ]);

  const handleTime = (value: string) => {
    activePeriod.value = value;
    getApiData();
  };
  const goto = (path: string) => {
    router.push({ path: `mib/${path}` });
  };
  // const emit = defineEmits(['openDialog']);
  // const openDetail = (item: any) => {
  //   emit('openDialog', item);
  // };
  const getData = async () => {
    const colorRgba = ['#65A7F6', '#ffdd00'];
    const xAxis = chartData.value[0].data.map((item) =>
      dayjs(item.collectDateTime).format('MM-DD'),
    );
    // 自定义Y轴刻度
    const yAxisBox = {
      type: 'value',
      name: `单位（mg/L）`,
      min: 0,
      max: 140,
      axisLabel: {
        show: true,
        formatter: function (value) {
          if (value === 0) return '0';
          else if (value === 30) return '30';
          else if (value === 60) return '60';
          else if (value === 90) return '90';
          else if (value === 120) return '100+';
          else return '';
        },
        color: '#000',
        fontSize: 18,
      },
      nameTextStyle: {
        padding: [0, 0, 0, getScaleValByClientWidth(-52)],
        color: '#000',
        fontSize: 18,
        align: 'left',
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: function (value) {
            if (value === 30) return '#0FBB4E'; // 黄色刻度线
            if (value === 60) return '#B9A700'; // 橙色刻度线
            if (value === 90) return '#DE7F0B'; // 红色刻度线
            if (value === 140) return '#DC1711'; // 红色刻度线
            return '#B3B1B1'; // 默认灰色
          },
          // type: function (value) {
          //   if (value === 60 || value === 100) return 'solid'; // 预警线用实线
          //   return 'dashed';
          // },
          // width: function (value) {
          //   if (value === 60 || value === 100) return 2; // 预警线加粗
          //   return 1;
          // },
        },
      },
      axisTick: {
        show: false,
      },
      // 自定义刻度位置
      splitArea: {
        show: false,
      },
      data: [0, 30, 60, 90, 140],
    };
    const option: any = {
      color: colorRgba,
      tooltip: {
        trigger: 'axis',
        appendTo: () => document.body,
        renderMode: 'html',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: '#fff',
        textStyle: {
          color: '#000',
          fontSize: 14,
          // lineHeight: 28,
          // height: 28,
          fontWeight: 400,
        },
        borderColor: 'transparent',
        // formatter: (params) => {
        //   const item = params.filter((item) => item.value !== undefined);
        //   if (item.length) {
        //     let htmlStr = ``;
        //     item.forEach((val, index) => {
        //       htmlStr += `<div style="color: #999;">${index === 0 ? val.name : ''}</div>
        //           ${val.marker} <span style="display: inline-block; width: 30px;">${
        //         val.seriesName
        //       }</span><span  style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${
        //         val.value || val.value == 0 ? Number(val.value) : '-'
        //       }${
        //         // tooltipNameBox.find((item: any) => item.indexName === val.seriesName)?.unitName ??
        //         ''
        //       }</span>`;
        //     });
        //     return htmlStr;
        //   }
        //   return;
        // },
      },
      legend: {
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 24,
        color: colorRgba,
        top: 21,
        data: [], // chartData.value.map((item) => item.indexName),
        textStyle: {
          fontSize: 14,
          color: '#000',
        },
      },
      grid: {
        top: '25%',
        left: '3',
        right: '76',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: true,
          data: xAxis,
          // data: props.dataList[0].XAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#B3B1B1',
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#000',
              fontSize: 18,
            },
          },
          axisPointer: {
            type: 'shadow',
          },
        },
      ],
      yAxis: yAxisBox,
      series: [
        ...chartData.value.map((item, index) => {
          const data = {
            name: item.indexName,
            type: item.type ? item.type : 'bar',
            label: {
              show: item.seriesLabel ? item.seriesLabel : false,
              position: 'top',
              color: '#333333',
            },
            barMaxWidth: '20%',
            barGap: '10%',
            itemStyle: {
              color: colorRgba[index],
              barBorderRadius: [2, 2, 0, 0],
            },
            data: item.data ? item.data.map((i) => (i.value == null ? null : Number(i.value))) : [],
            yAxisIndex: 0,
            markLine: {
              silent: true,
              lineStyle: {
                color: 'transparent',
              },
              data: [
                // {
                //   yAxis: 0,
                //   symbol: 'none',
                //   label: {
                //     show: false,
                //     // position: 'insideEndTop',
                //     formatter: '正常',
                //     color: '#0FBB4E',
                //     fontSize: 18,
                //     // fontWeight: 'bold',
                //     // backgroundColor: 'rgba(255, 107, 0, 0.1)',
                //     // padding: [2, 6],
                //     // borderRadius: 4,
                //   },
                //   lineStyle: {
                //     color: '#0FBB4E',
                //   },
                // },
                // {
                //   yAxis: 30,
                //   symbol: 'none',
                //   label: {
                //     show: false,
                //     // position: 'insideEndTop',
                //     formatter: '正常',
                //     color: '#0FBB4E',
                //     fontSize: 18,
                //     // fontWeight: 'bold',
                //     // backgroundColor: 'rgba(255, 107, 0, 0.1)',
                //     // padding: [2, 6],
                //     // borderRadius: 4,
                //   },
                //   lineStyle: {
                //     color: '#0FBB4E',
                //   },
                // },
                {
                  yAxis: 30,
                  symbol: 'none',
                  label: {
                    show:
                      alertWarnLevel.value === '2' ||
                      alertWarnLevel.value === '1' ||
                      alertWarnLevel.value === '3',
                    // position: 'insideEndTop',
                    formatter: '三级预警',
                    color: '#B9A700',
                    fontSize: 18,
                    // fontWeight: 'bold',
                    // backgroundColor: 'rgba(255, 107, 0, 0.1)',
                    // padding: [2, 6],
                    // borderRadius: 4,
                  },
                  lineStyle: {
                    color:
                      alertWarnLevel.value === '2' ||
                      alertWarnLevel.value === '1' ||
                      alertWarnLevel.value === '3'
                        ? '#B9A700'
                        : 'transparent',
                  },
                },
                {
                  yAxis: 60,
                  symbol: 'none',
                  label: {
                    show: alertWarnLevel.value === '2' || alertWarnLevel.value === '1',
                    // position: 'insideEndTop',
                    formatter: '二级预警',
                    color: '#DE7F0B',
                    fontSize: 18,
                    // fontWeight: 'bold',
                    // backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    // padding: [2, 6],
                    // borderRadius: 4,
                  },
                  lineStyle: {
                    color:
                      alertWarnLevel.value === '2' || alertWarnLevel.value === '1'
                        ? '#DE7F0B'
                        : 'transparent',
                  },
                },
                {
                  yAxis: 100,
                  symbol: 'none',
                  label: {
                    show: alertWarnLevel.value === '1',
                    // position: 'insideEndTop',
                    formatter: '一级预警',
                    color: '#DC1711',
                    fontSize: 18,
                    // fontWeight: 'bold',
                    // backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    // padding: [2, 6],
                    // borderRadius: 4,
                  },
                  lineStyle: {
                    color: alertWarnLevel.value === '1' ? '#DC1711' : 'transparent',
                  },
                },
              ],
            },
          };
          console.log('seriesData', data);
          return data;
        }),
      ],
      animation: false,
    };
    // console.log('option', option);
    setOptions(option);
    // setOptions(dataArr[0].option);
  };
  const getApiData = async () => {
    try {
      EmptyFlag.value = false;
      const res = await historyMonitoringChartData({
        startDate: dayjs().subtract(Number(activePeriod.value), 'day').format('YYYY-MM-DD'),
        endDate: dayjs().format('YYYY-MM-DD'),
        deviceId: '002',
      });
      console.log('historical-monitoring-data', res);
      if (res && res.data.length) {
        alertWarnLevel.value = res.level;
        chartData.value = res.data;
      } else {
        EmptyFlag.value = true;
      }
      getData();
    } catch (err) {
      EmptyFlag.value = true;
    }
  };
  onMounted(() => {
    getApiData();
  });
  const props = defineProps({
    updata: {
      type: Number,
      default: 1,
    },
  });
  watch(
    () => props.updata,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        getApiData();
      }
    },
  );
</script>
<style lang="less" scoped>
  .historical-monitoring-data {
    width: 100%;

    .custom-card {
      border-radius: 16px;
      border: 2px solid #ffffff;
      background: rgba(247, 250, 254, 1);

      :deep(.base-header) {
        height: 56px;
        padding: 16px 24px;
      }

      :deep(.base-header_line) {
        border-bottom: 1px solid #bcc5ce;
      }

      :deep(.card-body) {
        padding: 20px 24px;

        .card_empty {
          min-height: 164px;
        }
      }

      .text__header {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 24px;
        line-height: 24px;
        color: #000000;
        position: relative;
        cursor: pointer;
      }

      .text__next {
        height: 32px;
        cursor: pointer;
      }
    }

    .charts-box {
      min-height: 164px;
      position: relative;
      width: 100%;

      .bar-charts {
        width: 100%;
        min-height: 164px;
        height: 100%;
      }

      .btn-box {
        position: absolute;
        height: 40px;
        top: 0;
        right: 0;
        border-radius: 8px;
        cursor: pointer;
        display: flex;

        .btn-radio {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #ffffff;
          line-height: 40px;
          width: 86px;
          height: 40px;
          border: 1px solid #d9d9d9;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;

          &:hover {
            background: #f0f0f0;
          }

          &.active {
            transition: all 0.3s;
            background: #0b62cb;
            border: 1px solid #0b62cb;
            color: #fff;
          }

          &:first-child {
            border-radius: 8px 0 0 8px;
            border-right: none;
          }

          &:last-child {
            border-radius: 0px 8px 8px 0;
            border-left: none;
          }
        }
      }
    }
  }
</style>
