.date-picker-dark {
  background-color: #132b4e !important;
  color: #fff;
}

.date-picker-dropdown-dark .ant-picker-panel-container {
  background-color: #132b4e !important;

  .ant-picker-header {
    color: #fff;

    button {
      color: #fff;
    }
  }

  thead th {
    color: #fff;
  }

  tbody {
    td {
      width: 28px;
    }

    .ant-picker-cell {
      color: #9c9c9c;
    }

    .ant-picker-cell-in-view {
      color: #fff;

      &:hover .ant-picker-cell-inner {
        background: rgba(255 255 255 / 16%) !important;
        // background: rgb(11 98 203 / 88%) !important;
      }
    }

    .ant-picker-cell-selected {
      color: #fff;

      &:hover .ant-picker-cell-inner {
        background: #0b62cb !important;
        // background: rgb(11 98 203 / 88%) !important;
      }
    }

    .ant-picker-cell-disabled {
      pointer-events: unset;
      cursor: no-drop;

      &:hover .ant-picker-cell-inner {
        background: unset !important;
      }
    }

    .ant-picker-cell-today .ant-picker-cell-inner::before {
      border: none;
    }
  }
}
