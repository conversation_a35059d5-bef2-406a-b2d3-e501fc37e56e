<template>
  <div class="table-kit">
    <a-form
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      :colon="false"
      labelAlign="left"
      autocomplete="off"
    >
      <template v-for="(formItem, formIndex) in formList" :key="formIndex">
        <a-form-item label="表格标题">
          <a-input v-model:value="formItem.data.kitDisplayName" />
        </a-form-item>
        <a-form-item label="标题字体大小">
          <a-input-number
            v-model:value="formItem.data.businessData.tableConfig.style.title.fontSize"
            :min="1"
            :precision="0"
          />
        </a-form-item>

        <a-form-item label="标题字体颜色">
          <color-picker
            v-model:value="formItem.data.businessData.tableConfig.style.title.color"
            :input="true"
          />
        </a-form-item>

        <a-form-item label="表头字体大小">
          <a-input-number
            v-model:value="formItem.data.businessData.tableConfig.style.header.fontSize"
            :min="1"
            :precision="0"
          />
        </a-form-item>
        <a-form-item label="表体字体大小">
          <a-input-number
            v-model:value="formItem.data.businessData.tableConfig.style.body.fontSize"
            :min="1"
            :precision="0"
          />
        </a-form-item>
        <a-form-item label="是否显示">
          <a-radio-group
            v-model:value="formItem.data.displayMode"
            :options="displayOptions"
            @change="onDisplayChange"
          />
        </a-form-item>
        <a-row>
          <a-col :span="24">
            <a-form-item label="外框类型">
              <a-select v-model:value="formItem.data.frameType" @change="onFrameTypeChange">
                <a-select-option
                  v-for="(item, borderIndex) in borderOptions"
                  :key="borderIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formItem.data.frameType === 1">
            <a-form-item label="边框图片">
              <UploadCardBg
                :url="formItem.data.frameBackground"
                :frameBackgroundType="formItem.data.frameBackgroundType"
                @background-type-change="handleBackgroundTypeChange"
                @background-url-change="handleBackgroundUrlChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-show="formItem.data.frameType === 1">
            <a-form-item label="背景大小">
              <div> 宽度：{{ imgSize.width }} 高度：{{ imgSize.height }} </div>
            </a-form-item>
          </a-col>
          <!-- && formItem.data.frameBackgroundType === frameBackgroundTypeEnum.CUSTOM -->
          <a-col :span="24" v-show="formItem.data.frameType === 1">
            <a-form-item label="边框内边距">
              <a-row :gutter="[4, 8]" wrap>
                <a-col :span="10">
                  <a-input-number
                    placeholder="上"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[0]"
                  />
                </a-col>
                <!-- <a-col :span="10" v-if="false"> -->
                <a-col :span="10">
                  <a-input-number
                    placeholder="右"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[1]"
                  />
                </a-col>
                <!-- <a-col :span="10" v-if="false"> -->
                <a-col :span="10">
                  <a-input-number
                    placeholder="下"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[2]"
                  />
                </a-col>
                <a-col :span="10">
                  <a-input-number
                    placeholder="左"
                    :min="0"
                    :precision="0"
                    v-model:value="formItem.data.framePadding[3]"
                  />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>

          <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
            <a-form-item label="外框方位">
              <a-select v-model:value="formItem.data.framePlacement" @change="onPlacementChange">
                <a-select-option
                  v-for="(item, placementIndex) in placementOptions"
                  :key="placementIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
            <a-form-item label="外框横坐标">
              <a-input-number style="width: 100%" v-model:value="formItem.data.positionX" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-show="typeof formItem.data.frameType === 'number'">
            <a-form-item label="外框纵坐标">
              <a-input-number style="width: 100%" v-model:value="formItem.data.positionY" />
            </a-form-item>
          </a-col>
          <template v-if="false">
            <a-col :span="24" v-show="formItem.data.frameType === 1">
              <a-form-item label="外框背景">
                <HUpload
                  style="width: 100%"
                  accept="jpeg,jpg,png,gif"
                  :show-file-name="false"
                  :max-size="18"
                  :dragger="false"
                  :isDownload="false"
                  :api="uploadApi"
                  v-model:value="fileList"
                  @change="handleFrameBackgroundChange"
                >
                  <template #upload-icon>
                    <div>
                      <a-button type="primary" :ghost="Boolean(fileList?.length)">
                        {{ fileList?.length ? '重新上传' : '+点击上传' }}
                      </a-button>
                    </div>
                  </template>
                  <template #tips>
                    <div class="tip-box" title="请上传同一种风格的背景图片">
                      <span>请上传同一种风格的背景图片</span>
                    </div>
                  </template>
                </HUpload>
              </a-form-item>
            </a-col>
            <a-col :span="24" v-show="formItem.data.frameType === 1">
              <a-form-item label="背景大小">
                <div> 宽度：{{ imgSize.width }} 高度：{{ imgSize.height }} </div>
              </a-form-item>
            </a-col>
            <a-col :span="24" v-show="formItem.data.frameType === 1">
              <a-form-item label="外框宽度">
                <a-input-number
                  style="width: 100%"
                  :min="1"
                  :precision="0"
                  v-model:value="formItem.data.frameWidth"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24" v-show="formItem.data.frameType === 1">
              <a-form-item label="外框高度">
                <a-input-number
                  style="width: 100%"
                  :min="1"
                  :precision="0"
                  v-model:value="formItem.data.frameHeight"
                />
              </a-form-item>
            </a-col>
          </template>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="指标接口">
              <a-select v-model:value="formItem.data.businessData.resourceInterfaceId">
                <a-select-option
                  v-for="(item, interfaceIndex) in interfaceOptions"
                  :key="interfaceIndex"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="数据集">
              <TreeSelect
                v-model:value="formItem.data.businessData.dataset"
                show-search
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                placeholder="请选择数据集"
                allow-clear
                tree-default-expand-all
                :tree-data="dataset"
                tree-node-filter-prop="label"
                @change="onTreeSelectChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formItem.data.businessData.dataset">
            <a-form-item label="维度">
              <a-checkbox-group
                v-model:value="formItem.data.businessData.dimensions"
                :options="dimensionOptions"
                @change="handleCheckGroupChange"
              />
            </a-form-item>
          </a-col>
          <!-- <a-col>
          <div>{{ dimensions }}</div>
        </a-col> -->
          <a-col :span="24" v-if="formItem.data.businessData.dimensions?.length">
            <a-form-item label="显示内容">
              <draggable :list="dimensions" item-key="id" group="drag" animation="300">
                <template #item="{ element, index }">
                  <DimensionItem
                    :data="element"
                    :index="index"
                    @change-col-config="handleChangeColConfig"
                  />
                </template>
              </draggable>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formItem.data.businessData.dimensions?.length">
            <a-form-item label="表头颜色">
              <color-picker
                v-model:value="formItem.data.businessData.tableConfig.headerColor"
                :input="true"
                :setDefault="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formItem.data.businessData.dimensions?.length">
            <a-form-item label="表头背景色">
              <color-picker
                v-model:value="formItem.data.businessData.tableConfig.headerBackground"
                :input="true"
                :setDefault="false"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="expressions" v-if="!isEmpty(datasetNode)">
          <a-col :span="24">
            <a-collapse
              class="collapseRef"
              ghost
              :bordered="false"
              v-model:activeKey="activeKey"
              expand-icon-position="right"
            >
              <a-collapse-panel
                v-for="(item, expressionIndex) in formItem.data.businessData.displayTableDataInfos"
                :key="`expression${expressionIndex + 1}`"
                :header="getCollapsePanelHeader(item)"
              >
                <template #extra>
                  <img
                    :src="AddBorderUrl"
                    class="w-[20px] mr-10px"
                    v-if="
                      indexGroupInfoBySelecteDimension.length >
                      formItem.data.businessData.displayTableDataInfos.length
                    "
                    @click.stop="addExpression(expressionIndex)"
                  />
                  <img
                    :src="DelBorderUrl"
                    class="w-[20px]"
                    @click.stop="delExpression(expressionIndex)"
                  />
                </template>
                <a-form-item label="指标CODE">
                  <div class="flex justify-between items-center">
                    <a-select
                      style="width: 80%"
                      v-model:value="item.code"
                      placeholder="请选择指标"
                      :disabled="item.code ? true : false"
                      show-search
                      :filter-option="filterIndicatorOption"
                      @change="(code) => handleIndicatorSelectChange(code, expressionIndex)"
                    >
                      <a-select-option
                        v-for="indicator in availableIndicatorOptions"
                        :key="indicator.code"
                        :value="indicator.code"
                      >
                        {{ indicator.displayName }} ({{ indicator.code }})
                      </a-select-option>
                    </a-select>

                    <a class="ant-dropdown-link" @click.prevent="copyToClipboard(item.code)">
                      复制
                    </a>
                  </div>
                </a-form-item>
                <a-form-item label="指标值">
                  <a-input
                    style="width: 100%"
                    v-model:value="item.expression"
                    @blur="() => handleExpressionChange(expressionIndex, item)"
                  />
                </a-form-item>
                <a-col :span="24">
                  <a-form-item label="是否显示诊断">
                    <a-switch v-model:checked="item.isWarning" />
                  </a-form-item>
                </a-col>
                <a-form-item label="表达式类型">
                  <a-select
                    v-model:value="item.scriptType"
                    :allowClear="true"
                    @change="handleScriptTypeChange($event, expressionIndex, item)"
                  >
                    <a-select-option
                      v-for="expressionType in expressionTypeOptions"
                      :key="expressionType.value"
                      :value="expressionType.value"
                    >
                      {{ expressionType.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item
                  label="报警规则"
                  v-show="
                    item.scriptType == expressionTypeEnum.rule &&
                    item.scriptType !== expressionTypeEnum.regular
                  "
                >
                  <a-select
                    v-model:value="item.warningRuleId"
                    :indicatorReq="getCurrentWarnRuleList(expressionIndex, item)"
                    :allowClear="true"
                  >
                    <a-select-option
                      v-for="warnRule in indicatorCodeOptions[expressionIndex].value"
                      :key="warnRule.value"
                      :value="warnRule.value"
                    >
                      {{ warnRule.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item
                  label="计算表达式"
                  v-show="item.scriptType && item.scriptType !== expressionTypeEnum.regular"
                >
                  <a-button style="width: 100%" @click="openEditorModal(item, expressionIndex)">
                    ...
                  </a-button>
                </a-form-item>
                <a-form-item label="单位">
                  <a-input v-model:value="item.unit" />
                </a-form-item>
                <a-col :span="24">
                  <a-form-item label="显示名称">
                    <a-input v-model:value="item.displayName" />
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="24">
                  <a-form-item label="显示别名">
                    <a-input v-model:value="item.shortDisplayName" />
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item label="启用别名">
                    <a-switch v-model:checked="item.isShowShort" />
                  </a-form-item>
                </a-col> -->
                <a-row
                  v-if="item.isNumber === true && item.scriptType === expressionTypeEnum.regular"
                >
                  <!-- 数值型 -->
                  <a-row style="width: 100%">
                    <a-col :span="8">
                      <div style="line-height: 32px">下限值</div>
                    </a-col>
                    <a-col :span="10">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <a-input-number style="width: 100%" v-model:value="item.lowerLimits" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <div style="text-align: right">
                          <a-checkbox v-model:checked="item.isLowerEqual"> 可等 </a-checkbox>
                        </div>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row style="width: 100%">
                    <a-col :span="8">
                      <div style="line-height: 32px">上限值</div>
                    </a-col>
                    <a-col :span="10">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <a-input-number style="width: 100%" v-model:value="item.upperLimits" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <div style="text-align: right">
                          <a-checkbox v-model:checked="item.isUpperEqual"> 可等 </a-checkbox>
                        </div>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-col :span="24">
                    <a-form-item label="常规颜色">
                      <color-picker
                        v-model:value="item.normalColor"
                        :input="true"
                        :setDefault="false"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item label="超上限颜色">
                      <color-picker
                        v-model:value="item.greaterThanUpperColor"
                        :input="true"
                        :setDefault="false"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="24">
                    <a-form-item label="超下限颜色">
                      <color-picker
                        v-model:value="item.lowerThanLowerColor"
                        :input="true"
                        :setDefault="false"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                <template v-if="item.IsNumber === false">
                  <!-- 文字型 -->
                  <a-row
                    :gutter="16"
                    v-for="(valueItem, valueIndex) in item.values"
                    :key="valueIndex"
                  >
                    <a-col :span="24" style="padding-bottom: 8px">
                      <div class="value-wrapper">
                        <span>预设值</span>
                        <div class="action">
                          <img
                            v-show="valueIndex === 0"
                            :src="AddBorderUrl"
                            style="width: 20px"
                            @click="addValue(index)"
                          />
                          <img
                            v-show="valueIndex !== 0"
                            :src="DelBorderUrl"
                            style="width: 20px"
                            @click="delValue(index, valueIndex)"
                          />
                        </div>
                      </div>
                    </a-col>
                    <a-col :span="18">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <a-input v-model:value="valueItem.value" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item :wrapper-col="{ span: 24 }">
                        <color-picker
                          v-model:value="valueItem.color"
                          :input="true"
                          :setDefault="false"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
              </a-collapse-panel>
            </a-collapse>
          </a-col>
        </a-row>
      </template>
    </a-form>
    <BasicModal v-model:open="visible" title="Javascript" :width="800" :bodyStyle="{ padding: 0 }">
      <div class="text-right pb-3">
        <a-button type="second" @click="createTemplate"> 生成模版 </a-button>
      </div>
      <div style="height: 400px">
        <json-editor v-if="visible" v-model="scriptStr" ref="editorRef" />
      </div>
      <template #footer>
        <a-button @click="handleCancel"> 取消 </a-button>
        <a-button type="primary" @click="handleOk"> 确定 </a-button>
      </template>
    </BasicModal>
  </div>
</template>

<script>
  import {
    Form,
    FormItem,
    InputNumber,
    Select,
    SelectOption,
    RadioGroup,
    Row,
    Col,
    Collapse,
    CollapsePanel,
    Switch,
    Checkbox,
    CheckboxGroup,
    TreeSelect,
  } from 'ant-design-vue';
  import { ColorPicker } from '/@/components/ColorPicker';
  import DimensionItem from './DimensionItem.vue';
  import { HUpload } from '/@/components/Upload';
  import JsonEditor from '../../JsonEditor.vue';
  import { BasicModal } from '/@/components/Modal';
  import AddBorderUrl from '/@process-editor/assets/images/add-border.png';
  import DelBorderUrl from '/@process-editor/assets/images/del-border.png';

  const AForm = Form;
  const AFormItem = FormItem;
  const AInputNumber = InputNumber;
  const ASelect = Select;
  const ASelectOption = SelectOption;
  const ARadioGroup = RadioGroup;
  const ARow = Row;
  const ACol = Col;
  const ACollapse = Collapse;
  const ACollapsePanel = CollapsePanel;
  const ASwitch = Switch;
  const ACheckbox = Checkbox;
  const ACheckboxGroup = CheckboxGroup;

  export default {};
</script>

<script setup>
  import { ref, onMounted, watch, computed, reactive, nextTick } from 'vue';
  import { cloneDeep, isEmpty } from 'lodash-es';
  import draggable from 'vuedraggable';
  // import SortTable from 'sortablejs';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { updatePositionNoBorder, updatePositionHasBorder } from '/@process-editor/core/kits/data';
  import { COL_MIN_WIDTH } from '/@process-editor/core/kits/constant';
  import {
    getKitDataSetTreeApi,
    getPlatformListApi,
    uploadApi,
    getResourceInterfacePage,
    getWarnEventPage,
  } from '/@process-editor/api/index';
  import {
    displayOptions,
    initExpression,
    initValue,
    borderOptions,
    placementOptions,
    colTypeOptions,
    expressionTypeOptions,
    expressionTypeEnum,
  } from '../kit.data';
  import { updatePropData } from '../utils';
  import { getImgSize } from '/@process-editor/core/share';
  import UploadCardBg from '../components/UploadCardBg.vue';
  import { frameBackgroundTypeEnum } from '../kit.data';
  import { addResourcePrefix } from '/@process-editor/utils/index';
  import cardBg1 from '/@process-editor/assets/images/card-bg1.png';
  import cardBg2 from '/@process-editor/assets/images/card-bg2.png';

  const props = defineProps({
    formList: {
      type: Array,
      default: () => [
        {
          data: {},
        },
      ],
    },
    data: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    activePen: {
      type: Object,
      default: () => {},
    },
  });
  const UPDATE_DATA = 'update:data';
  const emits = defineEmits(['change-view-mode', 'update:data']);

  const { createMessage } = useMessage();

  const activeKey = ref(['expression1']);

  onMounted(() => {
    meta2d.on('editPen', () => {
      onPlacementChange(props.data.framePlacement);
    });

    const url = props.data.frameBackground || '';
    fileList.value = [{ url, fileName: url }];
    // initSortTable();
  });

  const platformId = ref('');
  async function getPlatformList() {
    const data = await getPlatformListApi();
    return data;
  }

  const interfaceOptions = ref([]);
  async function getInterfaceList() {
    // current=1&size=10&resourceIndexId=1&factoryId=2024&orgId=waterBusiness&appId=station&renterId=2024
    const params = {
      current: 1,
      pageSize: 999,
      resourceIndexId: 1,
    };
    const data = await getResourceInterfacePage(params);
    interfaceOptions.value = (data.records || []).map((i) => ({
      label: i.interfaceName,
      value: i.id,
    }));
    console.log('interfaceOptions', interfaceOptions.value);
  }
  getInterfaceList();

  const dataset = ref([]);
  async function getDatasetData() {
    const params = {
      platformId: platformId.value,
      groupPurpose: 2,
    };
    const data = await getKitDataSetTreeApi(params);
    return data;
  }

  const datasetNode = ref({});
  async function initData() {
    if (!dataset.value.length) {
      const platformList = await getPlatformList();
      platformId.value = platformList[0]?.id || '';
      const _dataset_ = await getDatasetData();
      dataset.value = _dataset_.map((i) => ({ ...i, disabled: true }));
    }

    if (props.data.businessData.dataset) {
      const node = getNode(dataset.value, props.data.businessData.dataset);
      dimensionOptions.value = getDimensionOptions(node.keys);
      datasetNode.value = node;
    }
    dimensions.value = props.data.businessData.tableConfig.header;
    // 更新可用的指标选项
    updateAvailableIndicatorOptions();
  }
  initData();

  watch(
    () => props.data.businessData.dataset,
    async () => {
      initData();
    },
  );

  watch(
    () => props.data.frameBackgroundType,
    async () => {
      const urlMap = new Map([
        [frameBackgroundTypeEnum.MODE1, cardBg1],
        [frameBackgroundTypeEnum.MODE2, cardBg2],
        // [frameBackgroundTypeEnum.CUSTOM, addResourcePrefix(props.data.frameBackground)],
      ]);
      const url = urlMap.get(props.data.frameBackgroundType);
      const size = await getImgSize(url);
      if (size) {
        imgSize.value.width = size.width;
        imgSize.value.height = size.height;
        updateFormDataByProp((data) => {
          const frameWidth = dimensions.value.reduce((prev, next) => prev + next.width, 0);
          data.frameWidth = imgSize.value.width || frameWidth;
          data.frameHeight = imgSize.value.height;
        });
      }
    },
    {
      immediate: true,
    },
  );

  // 全维度指标数据
  const indexGroupInfo = computed(() => {
    const indexKeys = datasetNode.value?.keys?.filter((i) => i.isIndexValue) || []; // 指标维度keysArr
    const indexValues = datasetNode.value?.values || [];

    const result = indexValues
      .map((item, index) => {
        const result = indexKeys.map(({ originName, label }) => {
          let code = item[originName];
          if (!code) return null;
          const indicator = {
            displayName: item.displayName,
            key: originName,
            code: code,
            group: label,
            unit: item.unit,
          };

          return indicator;
        });

        return result;
      })
      .flat()
      .filter((i) => i);

    return result;
  });

  const indexGroupInfoBySelecteDimension = computed(() => {
    return getSelectDimensionIndicatorArr();
  });

  function getCollapsePanelHeader(item) {
    let name = item.isShowShort ? item.shortDisplayName : item.displayName || '表达式';

    const findItem = indexGroupInfo.value.find((i) => i.code === item.code);
    const category = findItem?.group || '';

    return `${name}${category ? '(' : ''}${category}${category ? ')' : ''}`;
  }

  const imgSize = ref({ width: 0, height: 0 });
  const fileList = ref([]);
  const dimensions = ref([]);
  const dimensionOptions = ref([]);

  // 可用指标选项
  const availableIndicatorOptions = ref([]);

  async function handleFrameBackgroundChange([file]) {
    const size = await getImgSize(file?.url);
    if (size) {
      imgSize.value.width = size.width;
      imgSize.value.height = size.height;
    }

    updateFormDataByProp((data) => {
      const frameWidth = dimensions.value.reduce((prev, next) => prev + next.width, 0);
      data.frameWidth = imgSize.value.width || frameWidth;
      data.frameHeight = imgSize.value.height;
      data.frameBackground = file?.url || '';
    });
  }

  function getDimensionOptions(data) {
    return data.map((j) => ({
      ...j,
      label: j.originName,
      value: j.originName,
    }));
  }

  function getNode(dataset, value) {
    function treeFind(tree, func) {
      for (const data of tree) {
        if (func(data)) return data;
        if (data.children) {
          const res = treeFind(data.children, func);
          if (res) return res;
        }
      }
      return null;
    }
    const node = treeFind(dataset, (item) => item.value === value);

    return node;
  }

  function mergeIndicatorData(expression, data, isGenerateNew = true) {
    let mergeObj = {
      expression: `getV('${data.code}')`,
      code: data.code,
      displayName: data.displayName,
      unit: data.unit,
      groupCode: data.key,
    };
    return isGenerateNew ? { ...expression, ...mergeObj } : Object.assign(expression, mergeObj);
  }

  function onTreeSelectChange(value) {
    dimensions.value = [];

    let displayTableDataInfos = [{ ...initExpression, code: '' }];
    let values = [];
    const node = getNode(dataset.value, value);
    if (node) {
      dimensionOptions.value = getDimensionOptions(node.keys);
      values = node.values;
      datasetNode.value = node;
    }

    updateFormDataByProp((data) => {
      data.businessData.dimensions = [];
    });

    // 更新可用的指标选项
    updateAvailableIndicatorOptions();
  }

  function handleCheckGroupChange(checkedValue) {
    const names = dimensions.value.map((i) => i.originName);
    const result = checkedValue.map((i) => {
      const dimension = dimensionOptions.value.find((j) => j.originName === i);
      let item = {
        originName: dimension.originName,
        isIndexValue: dimension.isIndexValue,
        id: dimension.id,
        title: '',
        width: COL_MIN_WIDTH,
        type: colTypeOptions[0].value,
        color: '',
      };

      if (names.includes(i)) {
        item = dimensions.value.find((j) => j.originName === i);
      }
      return item;
    });
    dimensions.value = result;

    function getDisplayTableDataInfos() {
      const indexDimensions = dimensions.value.filter((i) => i.isIndexValue);
      const groupEnArr = indexDimensions.map((i) => i.originName);
      const data = cloneDeep(getSelectDimensionIndicatorArr());
      const result = data.map((i) => mergeIndicatorData(initExpression, i));
      return result;
    }

    updateFormDataByProp((data) => {
      if (data.frameType === 0) {
        data.frameWidth = dimensions.value.reduce((prev, next) => prev + next.width, 0);
      }
      data.businessData.tableConfig.header = dimensions.value;
      data.businessData.tableConfig.data = datasetNode.value.values;
      data.businessData.displayTableDataInfos = getDisplayTableDataInfos();
      // const length = data.businessData.displayTableDataInfos.length;
      activeKey.value = [];
    });

    // 更新可用的指标选项
    updateAvailableIndicatorOptions();
  }

  // 更新header内容
  function handleChangeColConfig(info) {
    Object.keys(info.data).forEach((key) => {
      dimensions.value[info.index][key] = info.data[key];
    });
    updateFormDataByProp((data) => {
      if (data.frameType === 0) {
        data.frameWidth = dimensions.value.reduce((prev, next) => prev + next.width, 0);
      }
      data.businessData.tableConfig.header = dimensions.value;
    });
  }

  function copyToClipboard(text) {
    navigator.clipboard.writeText(text);
    createMessage.success('复制成功');
  }

  // 过滤指标选项
  function filterIndicatorOption(input, option) {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  function getSelectDimensionIndicatorArr() {
    const dimensions = props.data.businessData.dimensions || [];
    return indexGroupInfo.value.filter((i) => dimensions.includes(i.key));
  }
  // 更新可用的指标选项，初始化、数据集变动、维度变动、指标变动、删除指标时更新
  async function updateAvailableIndicatorOptions() {
    let { promise, resolve } = Promise.withResolvers();
    nextTick(() => resolve());
    await promise;
    // 选择的维度，包含指标数据
    let selectedDimensionIndicatorData = getSelectDimensionIndicatorArr();
    // 获取已经选择的code值
    const usedCodes = props.data.businessData.displayTableDataInfos
      .map((item) => item.code)
      .filter((code) => code); // 过滤掉空值
    let availableIndicators = selectedDimensionIndicatorData.filter(
      (i) => !usedCodes.includes(i.code),
    );
    availableIndicatorOptions.value = availableIndicators;
  }

  // 处理指标选择变化
  function handleIndicatorSelectChange(code, expressionIndex) {
    debugger;
    if (!code) {
      // 如果清空选择，重置相关字段
      updateFormDataByProp((data) => {
        data.businessData.displayTableDataInfos[expressionIndex] = cloneDeep(initExpression);
      });
    } else {
      // 找到选中的指标数据
      const selectedIndicator = indexGroupInfo.value.find((indicator) => indicator.code === code);
      if (!selectedIndicator) return;
      // 更新表达式数据
      updateFormDataByProp((data) => {
        const item = data.businessData.displayTableDataInfos[expressionIndex];
        mergeIndicatorData(item, selectedIndicator, false);
      });
    }
    // 更新可用选项
    updateAvailableIndicatorOptions();
  }

  // function initSortTable() {
  //   const el = document.getElementsByClassName('collapseRef')[0];
  //   const options = {
  //     onEnd(evt) {
  //       updateFormDataByProp((data) => {
  //         const rawData = cloneDeep(data.businessData.displayTableDataInfos);
  //         data.businessData.displayTableDataInfos = swapElements(
  //           rawData,
  //           evt.oldIndex,
  //           evt.newIndex,
  //         );
  //       });
  //     },
  //   };
  //   const sortTable = new SortTable(el, options);

  //   return sortTable;
  // }

  /**
   * 交换数组中两个元素的位置
   * @param {Array} data 数组
   * @param {Number} index1 旧下标
   * @param {Number} index2 新下标
   * @returns {Array} 新数组
   */
  // function swapElements(data, index1, index2) {
  //   if (data.length < 2) return data;

  //   const array = cloneDeep(data);
  //   [array[index1], array[index2]] = [array[index2], array[index1]];

  //   return array;
  // }

  function updateFormDataByProp(cb) {
    const params = [props.data, emits, UPDATE_DATA];
    updatePropData(params, cb);
  }

  function onDisplayChange() {
    emits('change-view-mode');
  }

  function onFrameTypeChange() {
    if (props.data.frameType === 0) {
      updateFormDataByProp((data) => {
        console.log('data', data);

        data.frameWidth =
          data.businessData.tableConfig.header.reduce((prev, next) => prev + next.width, 0) || '';
      });
    }
  }

  function handleBackgroundTypeChange(frameBackgroundType) {
    updateFormDataByProp((data) => {
      data.frameBackgroundType = frameBackgroundType;
    });
  }

  function handleBackgroundUrlChange(frameBackground) {
    updateFormDataByProp(async (data) => {
      data.frameBackground = frameBackground;
      const url = addResourcePrefix(frameBackground);
      const size = await getImgSize(url);
      if (size) {
        imgSize.value.width = size.width;
        imgSize.value.height = size.height;
        const frameWidth = dimensions.value.reduce((prev, next) => prev + next.width, 0);
        data.frameWidth = imgSize.value.width || frameWidth;
        data.frameHeight = imgSize.value.height;
      }
      console.log('data-frameBackground', data);
    });
  }

  function addExpression(index) {
    let item = cloneDeep(initExpression);
    updateFormDataByProp((data) => {
      data.businessData.displayTableDataInfos.splice(index + 1, 0, item);
      activeKey.value = [];
    });
  }

  function delExpression(index) {
    updateFormDataByProp((data) => {
      data.businessData.displayTableDataInfos.splice(index, 1);
      activeKey.value = [];
    });

    // 更新可用的指标选项
    updateAvailableIndicatorOptions();
  }

  function addValue(index) {
    updateFormDataByProp((data) => {
      data.expressions[index].values.push(cloneDeep(initValue));
    });
  }

  function delValue(index, valueIndex) {
    updateFormDataByProp((data) => {
      data.expressions[index].values.splice(valueIndex, 1);
    });
  }

  function noBorderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionNoBorder(e, data, props.activePen);
    });
  }

  function borderPlacement(e) {
    updateFormDataByProp((data) => {
      updatePositionHasBorder(e, data, props.activePen);
    });
  }

  function onPlacementChange(e) {
    const type = props.data.frameType;
    switch (type) {
      case 0:
        noBorderPlacement(e);
        break;
      case 1:
        borderPlacement(e);
        break;
      default:
        break;
    }
  }

  const editorRef = ref(null);

  const visible = ref(false);
  const scriptStr = ref('');
  const lambdaIndex = ref(-1);
  const expressionItem = ref(null);
  function openEditorModal(record, index) {
    scriptStr.value = record.script;
    lambdaIndex.value = index;
    expressionItem.value = record;

    if (record.scriptType === expressionTypeEnum.rule) {
      handleScriptTypeChange(expressionTypeEnum.rule, index, record);
    }

    visible.value = true;
  }

  function handleOk() {
    visible.value = false;
    updateFormDataByProp((data) => {
      if (lambdaIndex.value > -1) {
        data.businessData.displayTableDataInfos[lambdaIndex.value].script = scriptStr.value;
      }
    });
  }

  function handleCancel() {
    visible.value = false;
    updateFormDataByProp((data) => {
      if (lambdaIndex.value > 0) {
        data.businessData.displayTableDataInfos[lambdaIndex].script = scriptStr.value;
      }
    });
    scriptStr.value = '';
    lambdaIndex.value = -1;
    expressionItem.value = null;
  }

  const scriptTemplate = ref('');
  function handleScriptTypeChange(scriptType, index, record) {
    if (scriptType === expressionTypeEnum.indicator) {
      const code = record?.code || 'CODE';
      const template = `
      if (getV('${code}') > 100) {
        return 'red';
      } else if (getV('${code}') > 50) {
        return 'yellow';
      } else {
        return '';
      }
    `;
      scriptTemplate.value = template;
    } else if (scriptType === expressionTypeEnum.rule) {
      const warningRuleId = record?.warningRuleId || '报警规则ID';

      const template = `
        if (getV('${warningRuleId}') == 1) {
          return 'red';
        } else if (getV('${warningRuleId}') == 2) {
          return 'orange';
        } else if (getV('${warningRuleId}') == 3) {
          return 'yellow';
        } else {
          return 'normal';
        }
      `;
      scriptTemplate.value = template;
    } else if (!scriptType) {
      scriptTemplate.value = '';
      scriptStr.value = '';
      updateFormDataByProp((data) => {
        data.businessData.displayTableDataInfos[index].script = '';
        data.businessData.displayTableDataInfos[index].warningRuleId = '';
      });
    }
  }

  function createTemplate() {
    const editor = editorRef.value;
    if (!scriptTemplate.value) {
      handleScriptTypeChange(
        expressionItem.value.scriptType,
        lambdaIndex.value,
        expressionItem.value,
      );
    }
    editor.setValue(scriptTemplate.value);
    editor.formatDocument();
  }

  let indicatorCodeOptions = reactive(
    new Array(999).fill('').map((i) => ({ value: [], isInit: false })),
  );
  async function getCurrentWarnRuleList(index, item, isFresh) {
    let indicatorCode = item.code;
    // indicatorCode = extractStringBetweenQuotes(indicatorCode);
    if (!indicatorCode) return;
    const params = {
      current: 1,
      pageSize: 999,
      indicatorCode,
      // indicatorCode: 'Smart.M580.GFJ.LCP2_DY_MINUTE_AVG',
    };
    if (!isFresh && indicatorCodeOptions[index].isInit) return;
    indicatorCodeOptions[index].isInit = true;
    const data = await getWarnEventPage(params);
    indicatorCodeOptions[index].value = (data.records || []).map((i) => ({
      label: i.title,
      value: i.id,
    }));
  }

  // 指标值变动，重新请求报警规则
  function handleExpressionChange(index, item) {
    // getCurrentWarnRuleList(index, item, true);
  }
</script>

<style lang="less" scoped>
  .tip-box {
    padding-top: 4px;
    color: #646a73;
    font-size: 12px;
  }
</style>
